#!/usr/bin/env python3
"""
Skript pro stahování titulků z YouTube videí nebo celých ka<PERSON>
"""
import os
import re
import time
import json
from pathlib import Path
from urllib.parse import urlparse, parse_qs
import subprocess
import sys


def install_yt_dlp():
    """Nainstaluje yt-dlp pokud není dostu<PERSON>ý."""
    try:
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("📦 Instaluji yt-dlp...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'yt-dlp'], check=True)
            return True
        except:
            print("❌ Nelze nainstalovat yt-dlp. Zkuste: pip install yt-dlp")
            return False


def is_youtube_url(url):
    """Zkontroluje, zda je URL z YouTube."""
    youtube_domains = ['youtube.com', 'youtu.be', 'www.youtube.com']
    parsed = urlparse(url)
    return any(domain in parsed.netloc for domain in youtube_domains)


def is_channel_url(url):
    """Zkontroluje, zda je URL kanál."""
    channel_patterns = [
        r'/channel/',
        r'/c/',
        r'/@',
        r'/user/',
        r'/playlist\?list='
    ]
    return any(pattern in url for pattern in channel_patterns)


def get_video_id(url):
    """Získá ID videa z URL."""
    parsed = urlparse(url)
    if 'youtu.be' in parsed.netloc:
        return parsed.path[1:]
    elif 'youtube.com' in parsed.netloc:
        query = parse_qs(parsed.query)
        return query.get('v', [None])[0]
    return None


def choose_languages():
    """Nechá uživatele vybrat jazyky titulků."""
    print("\n🌍 Vyberte jazyky titulků:")
    print("1️⃣  České (cs)")
    print("2️⃣  Anglické (en)")
    print("3️⃣  České a anglické (cs,en)")
    print("4️⃣  České, anglické a slovenské (cs,en,sk)")
    print("5️⃣  Vlastní výběr (zadáte kódy jazyků)")
    
    while True:
        choice = input("\nVaše volba (1-5): ").strip()
        if choice == '1':
            return 'cs'
        elif choice == '2':
            return 'en'
        elif choice == '3':
            return 'cs,en'
        elif choice == '4':
            return 'cs,en,sk'
        elif choice == '5':
            langs = input("Zadejte kódy jazyků oddělené čárkou (např. cs,en,de): ").strip()
            if langs:
                return langs
            print("❌ Musíte zadat alespoň jeden jazyk!")
        else:
            print("❌ Neplatná volba, zkuste znovu.")


def download_subtitles(url, output_dir, languages='cs,en', video_counter=None):
    """Stáhne titulky z daného URL."""
    # Získat info o videu pro lepší pojmenování
    print("   📊 Získávám informace o videu...")
    
    info_cmd = [
        'yt-dlp',
        '--dump-json',
        '--no-warnings',
        url
    ]
    
    video_title = "video"
    video_id = get_video_id(url) or "unknown"
    
    try:
        result = subprocess.run(info_cmd, capture_output=True, text=True)
        if result.returncode == 0 and result.stdout:
            info = json.loads(result.stdout)
            video_title = info.get('title', 'video')
            # Vyčistit název od problematických znaků
            video_title = re.sub(r'[^\w\s-]', '', video_title)
            video_title = re.sub(r'[-\s]+', '-', video_title)
            video_title = video_title[:80]  # Omezit délku
    except:
        pass
    
    # Přidat číslo videa pokud je to z kanálu
    # Použijeme konkrétní názvy souborů bez %(lang)s templatu
    if video_counter:
        base_name = f'{video_counter:03d}-{video_title}'
    else:
        base_name = video_title
    
    # Nejdřív zjistíme dostupné titulky
    list_cmd = [
        'yt-dlp',
        '--list-subs',
        '--no-warnings',
        url
    ]
    
    available_langs = []
    try:
        result = subprocess.run(list_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            # Parsovat dostupné jazyky z výstupu
            lines = result.stdout.split('\n')
            for line in lines:
                # Hledat řádky s jazykovými kódy
                if ' ' in line and not line.startswith('['):
                    parts = line.strip().split()
                    if parts and len(parts[0]) <= 5:  # Jazykové kódy jsou krátké
                        lang_code = parts[0]
                        if languages == 'all' or lang_code in languages.split(','):
                            available_langs.append(lang_code)
    except:
        pass
    
    # Pokud nejsou žádné požadované jazyky dostupné, zkusit alespoň něco
    if not available_langs and languages != 'all':
        available_langs = languages.split(',')[:1]  # Zkusit alespoň první jazyk
    
    # Stáhnout titulky pro každý jazyk zvlášť
    downloaded_files = []
    for lang in available_langs:
        output_file = str(output_dir / f'{base_name}.{lang}')
        
        cmd = [
            'yt-dlp',
            '--skip-download',
            '--write-subs',
            '--write-auto-subs',
            '--sub-langs', lang,
            '--sub-format', 'vtt/srt/best',
            '--output', output_file,
            '--no-warnings',
            url
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                # Najít stažené soubory
                files = list(output_dir.glob(f'{base_name}.{lang}.*'))
                downloaded_files.extend(files)
        except:
            pass
    
    return len(downloaded_files) > 0, downloaded_files


def get_channel_videos(channel_url):
    """Získá seznam všech videí z kanálu."""
    print("🔍 Získávám seznam videí z kanálu...")
    
    cmd = [
        'yt-dlp',
        '--flat-playlist',  # Pouze informace, ne stahování
        '--dump-json',
        '--no-warnings',
        channel_url
    ]
    
    videos = []
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if line:
                    try:
                        video_info = json.loads(line)
                        video_url = f"https://www.youtube.com/watch?v={video_info['id']}"
                        videos.append({
                            'url': video_url,
                            'title': video_info.get('title', 'Unknown'),
                            'id': video_info['id']
                        })
                    except:
                        pass
        return videos
    except Exception as e:
        print(f"❌ Chyba při získávání seznamu videí: {e}")
        return []


def choose_speed():
    """Nechá uživatele vybrat rychlost stahování."""
    print("\n🚀 Vyberte rychlost stahování:")
    print("1️⃣  Rychlé (bez prodlevy)")
    print("2️⃣  Střední (30 sekund mezi videi)")
    print("3️⃣  Pomalé (110 sekund mezi videi)")
    
    while True:
        choice = input("\nVaše volba (1-3): ").strip()
        if choice == '1':
            return 0
        elif choice == '2':
            return 30
        elif choice == '3':
            return 110
        else:
            print("❌ Neplatná volba, zkuste znovu.")


def main():
    """Hlavní funkce."""
    print("🎬 YouTube Subtitle Downloader")
    print("=" * 50)
    
    # Zkontrolovat/nainstalovat yt-dlp
    if not install_yt_dlp():
        return
    
    # Získat URL
    while True:
        url = input("\n📺 Zadejte URL YouTube videa nebo kanálu: ").strip().strip("'\"")
        
        if not url:
            print("❌ Musíte zadat URL!")
            continue
            
        if not is_youtube_url(url):
            print("❌ Toto není platná YouTube URL!")
            continue
            
        break
    
    # Získat výstupní složku
    while True:
        output_dir = input("\n📁 Zadejte cestu pro uložení titulků: ").strip().strip("'\"")
        
        if not output_dir:
            print("❌ Musíte zadat cestu!")
            continue
        
        output_path = Path(output_dir).expanduser()
        
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            break
        except Exception as e:
            print(f"❌ Nelze vytvořit složku: {e}")
    
    # Vybrat jazyky
    languages = choose_languages()
    
    # Rozhodnout, zda jde o kanál nebo video
    if is_channel_url(url):
        print("\n📺 Detekován YouTube kanál")
        
        # Získat seznam videí
        videos = get_channel_videos(url)
        
        if not videos:
            print("❌ Nepodařilo se získat seznam videí z kanálu.")
            return
            
        print(f"📊 Nalezeno {len(videos)} videí")
        
        # Vybrat rychlost
        delay = choose_speed()
        
        # Stáhnout titulky ze všech videí
        success_count = 0
        total_files = 0
        
        # Exponenciální backoff - uchovat původní a aktuální prodlevu
        original_delay = delay
        current_delay = delay
        
        for i, video in enumerate(videos, 1):
            print(f"\n[{i}/{len(videos)}] 🎯 Stahuji titulky z: {video['title'][:50]}...")
            
            success, files = download_subtitles(video['url'], output_path, languages, video_counter=i)
            
            if success and files:
                print(f"✅ Staženo {len(files)} souborů titulků")
                for file in files:
                    print(f"   📄 {file.name}")
                success_count += 1
                total_files += len(files)
                # Reset prodlevy na původní hodnotu po úspěchu
                current_delay = original_delay
            else:
                print(f"⚠️  Žádné titulky nenalezeny")
                # Zdvojnásobit prodlevu při selhání (max 30 minut)
                if current_delay > 0:
                    current_delay = min(current_delay * 2, 1800)
                    if current_delay != original_delay:
                        print(f"🔄 Prodleva zvýšena na {current_delay} sekund kvůli selhání")
            
            # Prodleva mezi stahováním (kromě posledního)
            if current_delay > 0 and i < len(videos):
                print(f"⏳ Čekám {current_delay} sekund...")
                time.sleep(current_delay)
        
        print(f"\n✨ Hotovo! Úspěšně staženy titulky z {success_count}/{len(videos)} videí")
        print(f"📄 Celkem staženo {total_files} souborů titulků")
        
    else:
        print("\n🎥 Detekováno jednotlivé video")
        
        # Stáhnout titulky z jednoho videa
        success, files = download_subtitles(url, output_path, languages)
        
        if success and files:
            print(f"\n✅ Úspěšně staženo {len(files)} souborů titulků:")
            for file in files:
                print(f"   📄 {file.name}")
        else:
            print("\n❌ Nepodařilo se stáhnout titulky z tohoto videa.")
    
    print(f"\n📁 Titulky uloženy do: {output_path}")


if __name__ == "__main__":
    main()