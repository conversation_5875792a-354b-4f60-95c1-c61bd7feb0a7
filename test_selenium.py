#!/usr/bin/env python3
"""
Test Selenium parseru pro HeroHero
"""

import logging
from parsers.herohero_selenium_parser import HeroheroSeleniumParser

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_selenium_parser():
    """Test Selenium parseru"""
    
    # Konfigurace
    source = {
        "name": "servactiporadi", 
        "url": "https://herohero.co/servactiporadi"
    }
    download_types = ["images", "videos", "documents"]
    
    # Vytvoř parser
    parser = HeroheroSeleniumParser(None, source["url"])
    
    # Parsuj obsah
    print(f"🚀 Testuji Selenium parser pro: {source['name']}")
    content_list = parser.parse_content_list(source, download_types)
    
    print(f"✅ Nalezeno {len(content_list)} položek:")
    for url, filename in content_list:
        print(f"  📄 {filename}")
        print(f"     {url}")

if __name__ == "__main__":
    test_selenium_parser()
