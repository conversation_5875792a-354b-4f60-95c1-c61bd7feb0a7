#!/usr/bin/env python3
"""
🚀 HeroHero Ultimate Downloader - <PERSON><PERSON><PERSON><PERSON> spo<PERSON><PERSON><PERSON>uje všechny dostupné metody pro stahování obsahu včetně nejnovějšího DRM
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import Dict, List

# Import našich modulů
from herohero_ultimate_downloader import HeroHeroUltimateDownloader
from drm_bypass import HeroHeroDRMBypass

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('herohero_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UltimateHeroHeroDownloader:
    """Kombinovaný downloader s všemi metodami"""
    
    def __init__(self):
        self.basic_downloader = HeroHeroUltimateDownloader()
        self.drm_bypass = HeroHeroDRMBypass()
        self.download_dir = Path("downloads")
        self.stats = {
            'total_posts': 0,
            'basic_downloads': 0,
            'drm_downloads': 0,
            'failed_downloads': 0,
            'skipped_downloads': 0
        }
    
    def run_complete_download(self):
        """Spusť kompletní stahování se všemi metodami"""
        print("🎯 HeroHero Ultimate Downloader")
        print("=" * 60)
        print("🔥 Stahuje VEŠKERÝ obsah včetně nejnovějšího DRM chráněného!")
        print("=" * 60)
        
        # Načti konfiguraci
        config = self._load_config()
        
        for source in config["sources"]:
            print(f"\n🎬 Zpracovávám: {source['name']}")
            print("-" * 40)
            
            try:
                # Fáze 1: Základní stahování (RSS, yt-dlp, web scraping)
                print("📥 Fáze 1: Základní metody...")
                basic_count = self._run_basic_download(source)
                self.stats['basic_downloads'] += basic_count
                
                # Fáze 2: Pokročilé DRM bypass
                print("🔓 Fáze 2: DRM bypass...")
                drm_count = self._run_drm_bypass(source)
                self.stats['drm_downloads'] += drm_count
                
                # Fáze 3: Fallback metody
                print("🛠️ Fáze 3: Fallback metody...")
                fallback_count = self._run_fallback_methods(source)
                
                total_source = basic_count + drm_count + fallback_count
                print(f"✅ {source['name']}: {total_source} položek staženo")
                
            except Exception as e:
                logger.error(f"❌ Chyba při zpracování {source['name']}: {e}")
                self.stats['failed_downloads'] += 1
        
        self._print_final_stats()
    
    def _run_basic_download(self, source: Dict) -> int:
        """Spusť základní stahování"""
        try:
            # Použij základní downloader
            original_sources = self.basic_downloader.config["sources"]
            self.basic_downloader.config["sources"] = [source]
            
            # Spočítej soubory před stahováním
            before_count = self._count_downloaded_files()
            
            # Spusť stahování
            self.basic_downloader.download_all_content()
            
            # Spočítej soubory po stahování
            after_count = self._count_downloaded_files()
            
            # Obnov původní konfiguraci
            self.basic_downloader.config["sources"] = original_sources
            
            return after_count - before_count
            
        except Exception as e:
            logger.error(f"Základní stahování selhalo: {e}")
            return 0
    
    def _run_drm_bypass(self, source: Dict) -> int:
        """Spusť DRM bypass"""
        try:
            # Získej seznam příspěvků
            post_urls = self._get_all_post_urls(source)
            drm_count = 0
            
            for post_url in post_urls:
                try:
                    print(f"🔍 Analyzuji DRM obsah: {post_url}")
                    
                    # Extrahuj DRM chráněný obsah
                    drm_content = self.drm_bypass.extract_drm_content(post_url)
                    
                    if drm_content:
                        print(f"🔒 Nalezeno {len(drm_content)} DRM položek")
                        
                        # Stáhni každou DRM položku
                        for content_info in drm_content:
                            output_dir = self.download_dir / "drm_content" / source['name']
                            output_dir.mkdir(parents=True, exist_ok=True)
                            
                            if self.drm_bypass.download_drm_content(content_info, output_dir):
                                drm_count += 1
                                print(f"✅ DRM obsah stažen: {content_info['type']}")
                            else:
                                print(f"❌ DRM stahování selhalo: {content_info['type']}")
                    
                    # Pauza mezi příspěvky
                    time.sleep(2)
                    
                except Exception as e:
                    logger.debug(f"DRM bypass selhal pro {post_url}: {e}")
                    continue
            
            return drm_count
            
        except Exception as e:
            logger.error(f"DRM bypass celkově selhal: {e}")
            return 0
    
    def _run_fallback_methods(self, source: Dict) -> int:
        """Spusť fallback metody pro obsah, který se nepodařilo stáhnout"""
        fallback_count = 0
        
        try:
            # Metoda 1: Browser automation s headless Chrome
            print("🌐 Zkouším browser automation...")
            browser_count = self._try_browser_automation(source)
            fallback_count += browser_count
            
            # Metoda 2: Screen recording pro DRM videa
            print("📹 Zkouším screen recording...")
            screen_count = self._try_screen_recording(source)
            fallback_count += screen_count
            
            # Metoda 3: Manual download hints
            print("💡 Generuji manuální download hints...")
            self._generate_manual_hints(source)
            
        except Exception as e:
            logger.error(f"Fallback metody selhaly: {e}")
        
        return fallback_count
    
    def _try_browser_automation(self, source: Dict) -> int:
        """Zkus browser automation pro zbývající obsah"""
        try:
            # Toto by implementovalo Selenium/Playwright automation
            # Pro demo účely vrátíme 0
            logger.info("Browser automation není implementována v této verzi")
            return 0
        except:
            return 0
    
    def _try_screen_recording(self, source: Dict) -> int:
        """Zkus screen recording pro DRM videa"""
        try:
            # Toto by implementovalo automatické screen recording
            # Pro demo účely vrátíme 0
            logger.info("Screen recording není implementován v této verzi")
            return 0
        except:
            return 0
    
    def _generate_manual_hints(self, source: Dict):
        """Vygeneruj hints pro manuální stahování"""
        try:
            hints_file = self.download_dir / f"{source['name']}_manual_hints.txt"
            
            with open(hints_file, 'w', encoding='utf-8') as f:
                f.write(f"🔧 Manuální stahování pro: {source['name']}\n")
                f.write("=" * 50 + "\n\n")
                f.write("Pro obsah, který se nepodařilo automaticky stáhnout:\n\n")
                f.write("1. Otevři prohlížeč a přihlas se na HeroHero\n")
                f.write(f"2. Jdi na: {source['url']}\n")
                f.write("3. Pro každé video:\n")
                f.write("   - Otevři Developer Tools (F12)\n")
                f.write("   - Jdi na záložku Network\n")
                f.write("   - Spusť video\n")
                f.write("   - Hledej .m3u8 nebo .mp4 soubory\n")
                f.write("   - Zkopíruj URL a použij: ffmpeg -i URL output.mp4\n\n")
                f.write("4. Pro DRM chráněná videa:\n")
                f.write("   - Použij OBS Studio pro screen recording\n")
                f.write("   - Nebo zkus: youtube-dl --cookies-from-browser brave URL\n\n")
            
            print(f"💡 Manuální hints uloženy: {hints_file}")
            
        except Exception as e:
            logger.error(f"Generování hints selhalo: {e}")
    
    def _get_all_post_urls(self, source: Dict) -> List[str]:
        """Získej všechny URLs příspěvků"""
        try:
            # Použij metodu z DRM bypass
            import requests
            session = requests.Session()
            
            # Načti cookies
            try:
                from auth_helper import AuthHelper
                cookies = AuthHelper.get_snap_brave_cookies('herohero.co')
                for name, value in cookies.items():
                    session.cookies.set(name, value, domain='.herohero.co')
            except:
                pass
            
            response = session.get(source['url'])
            if response.status_code == 200:
                import re
                creator_name = source['url'].split('/')[-1]
                pattern = rf'href="(/{creator_name}/posts/[^"]+)"'
                matches = re.findall(pattern, response.text)
                
                return [f"https://herohero.co{match}" for match in matches]
        
        except Exception as e:
            logger.debug(f"Získávání post URLs selhalo: {e}")
        
        return []
    
    def _count_downloaded_files(self) -> int:
        """Spočítej stažené soubory"""
        try:
            count = 0
            for root, dirs, files in os.walk(self.download_dir):
                count += len(files)
            return count
        except:
            return 0
    
    def _load_config(self) -> Dict:
        """Načti konfiguraci"""
        try:
            with open("config.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error("config.json nenalezen!")
            return {"sources": [], "download_types": ["images", "videos", "documents"]}
    
    def _print_final_stats(self):
        """Vytiskni finální statistiky"""
        print("\n" + "=" * 60)
        print("🎉 STAHOVÁNÍ DOKONČENO!")
        print("=" * 60)
        print(f"📊 STATISTIKY:")
        print(f"   📥 Základní stahování: {self.stats['basic_downloads']} položek")
        print(f"   🔓 DRM bypass: {self.stats['drm_downloads']} položek")
        print(f"   ❌ Selhání: {self.stats['failed_downloads']} zdrojů")
        print(f"   📁 Soubory uloženy v: {self.download_dir.absolute()}")
        print("\n💡 Pro obsah, který se nepodařilo stáhnout, zkontroluj *_manual_hints.txt soubory")
        print("🔥 Tento nástroj stahuje i nejnovější DRM chráněný obsah!")

def main():
    """Hlavní funkce"""
    try:
        downloader = UltimateHeroHeroDownloader()
        downloader.run_complete_download()
    except KeyboardInterrupt:
        print("\n⏹️ Stahování přerušeno uživatelem")
    except Exception as e:
        logger.error(f"Kritická chyba: {e}")
        print(f"❌ Kritická chyba: {e}")

if __name__ == "__main__":
    main()
