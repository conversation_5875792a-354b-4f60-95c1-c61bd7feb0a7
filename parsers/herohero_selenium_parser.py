#!/usr/bin/env python3
"""
Selenium parser pro Herohero.co platformu
Používá skutečný <PERSON>íž<PERSON>č pro načítání dynamického obsahu
"""

import re
import time
import logging
from typing import List, Tuple, Dict
from urllib.parse import urljoin, urlparse

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class HeroheroSeleniumParser:
    """Selenium parser pro Herohero.co platformu"""
    
    def __init__(self, session, base_url: str):
        self.session = session  # Pro kompatibilitu, ale nepoužíváme
        self.base_url = base_url
        self.domain = urlparse(base_url).netloc
        self.driver = None
        
    def parse_content_list(self, source: Dict, download_types: List[str]) -> List[Tuple[str, str]]:
        """
        Získá seznam obsahu ke stažení z Herohero stránky pomocí Selenium
        
        Args:
            source: Konfigurace zdroje
            download_types: Typy obsahu k stažení
            
        Returns:
            Seznam tuple (url, filename) pro stažení
        """
        content_list = []
        
        try:
            creator_name = self.base_url.split('/')[-1]
            
            # 1. Nastav Selenium driver
            self._setup_driver()
            
            # 2. Načti cookies z Brave
            self._load_cookies()
            
            # 3. Načti hlavní stránku
            logger.info(f"Načítám Herohero profil pomocí Selenium: {self.base_url}")
            self.driver.get(self.base_url)
            
            # 4. Počkej na načtení obsahu
            self._wait_for_content()
            
            # 5. Najdi a procházej příspěvky
            post_links = self._find_post_links()
            logger.info(f"Nalezeno {len(post_links)} příspěvků")
            
            # 6. Procházej každý příspěvek
            for i, post_link in enumerate(post_links):
                logger.info(f"Zpracovávám příspěvek {i+1}/{len(post_links)}")
                
                try:
                    # Načti příspěvek
                    self.driver.get(post_link)
                    self._wait_for_content()
                    
                    # Parsuj obsah
                    post_content = self._parse_post_content(creator_name, i, download_types)
                    content_list.extend(post_content)
                    
                    time.sleep(1)  # Pauza mezi příspěvky
                    
                except Exception as e:
                    logger.error(f"Chyba při zpracování příspěvku {i+1}: {e}")
                    continue
            
            logger.info(f"Celkem nalezeno {len(content_list)} položek ke stažení")
            
        except Exception as e:
            logger.error(f"Chyba při Selenium parsování: {str(e)}")
            
        finally:
            if self.driver:
                self.driver.quit()
                
        return content_list
    
    def _setup_driver(self):
        """Nastav Chrome driver s potřebnými opcemi"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Bez GUI
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Nastav Chrome driver
        self.driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
            options=chrome_options
        )
        
        logger.info("Selenium Chrome driver nastaven")
    
    def _load_cookies(self):
        """Načti cookies z Brave prohlížeče"""
        try:
            from auth_helper import AuthHelper
            
            # Nejdříve načti stránku, aby bylo možné nastavit cookies
            self.driver.get("https://herohero.co")
            
            # Získej cookies z Brave
            cookies = AuthHelper.get_snap_brave_cookies('herohero.co')
            
            # Nastav cookies v Selenium
            for name, value in cookies.items():
                self.driver.add_cookie({
                    'name': name,
                    'value': value,
                    'domain': '.herohero.co'
                })
            
            logger.info(f"Načteno {len(cookies)} cookies do Selenium")
            
        except Exception as e:
            logger.error(f"Chyba při načítání cookies: {e}")
    
    def _wait_for_content(self):
        """Počkaj na načtení obsahu stránky"""
        try:
            # Počkej na základní elementy
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Další čekání na dynamický obsah
            time.sleep(3)
            
        except TimeoutException:
            logger.warning("Timeout při čekání na obsah")
    
    def _find_post_links(self) -> List[str]:
        """Najdi odkazy na příspěvky na stránce"""
        post_links = []
        
        try:
            # Hledej různé selektory pro odkazy na příspěvky
            selectors = [
                "a[href*='/posts/']",
                "a[href*='posts']",
                "[data-testid*='post']",
                ".post-link",
                ".post-item a"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and '/posts/' in href:
                            post_links.append(href)
                except:
                    continue
            
            # Odstranit duplicity
            post_links = list(set(post_links))
            
            # Pokud nenajdeme odkazy, zkusíme scroll a hledání v HTML
            if not post_links:
                post_links = self._find_post_links_in_html()
            
        except Exception as e:
            logger.error(f"Chyba při hledání odkazů na příspěvky: {e}")
        
        return post_links
    
    def _find_post_links_in_html(self) -> List[str]:
        """Najdi odkazy na příspěvky v HTML kódu"""
        post_links = []
        
        try:
            html = self.driver.page_source
            creator_name = self.base_url.split('/')[-1]
            
            # Regex vzory pro odkazy na příspěvky
            patterns = [
                rf'href="(/{creator_name}/posts/[^"]+)"',
                rf'href="(https://herohero\.co/{creator_name}/posts/[^"]+)"',
                r'href="(/[^/]+/posts/[^"]+)"',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html)
                for match in matches:
                    if match.startswith('/'):
                        post_links.append('https://herohero.co' + match)
                    else:
                        post_links.append(match)
            
            # Odstranit duplicity
            post_links = list(set(post_links))
            
        except Exception as e:
            logger.error(f"Chyba při hledání odkazů v HTML: {e}")
        
        return post_links
    
    def _parse_post_content(self, creator_name: str, post_index: int, download_types: List[str]) -> List[Tuple[str, str]]:
        """Parsuje obsah jednotlivého příspěvku"""
        content_list = []
        
        try:
            # Získej HTML stránky
            html = self.driver.page_source
            
            # Najdi obrázky
            if 'images' in download_types:
                images = self._find_images_selenium(creator_name, post_index)
                content_list.extend(images)
            
            # Najdi videa
            if 'videos' in download_types:
                videos = self._find_videos_selenium(creator_name, post_index)
                content_list.extend(videos)
            
            # Najdi dokumenty
            if 'documents' in download_types:
                documents = self._find_documents_selenium(creator_name, post_index)
                content_list.extend(documents)
            
        except Exception as e:
            logger.error(f"Chyba při parsování příspěvku: {e}")
        
        return content_list
    
    def _find_images_selenium(self, creator_name: str, post_index: int) -> List[Tuple[str, str]]:
        """Najdi obrázky pomocí Selenium"""
        images = []
        
        try:
            # Najdi img elementy
            img_elements = self.driver.find_elements(By.TAG_NAME, "img")
            
            for i, img in enumerate(img_elements):
                try:
                    src = img.get_attribute('src') or img.get_attribute('data-src')
                    if src and self._is_content_image(src):
                        filename = self._generate_image_filename(src, creator_name, post_index, i)
                        images.append((src, filename))
                except:
                    continue
            
        except Exception as e:
            logger.error(f"Chyba při hledání obrázků: {e}")
        
        return images
    
    def _find_videos_selenium(self, creator_name: str, post_index: int) -> List[Tuple[str, str]]:
        """Najdi videa pomocí Selenium"""
        videos = []
        
        try:
            # Najdi video elementy
            video_elements = self.driver.find_elements(By.TAG_NAME, "video")
            for i, video in enumerate(video_elements):
                try:
                    src = video.get_attribute('src')
                    if src:
                        filename = self._generate_video_filename(src, creator_name, post_index, i)
                        videos.append((src, filename))
                except:
                    continue
            
            # Najdi source elementy ve video
            source_elements = self.driver.find_elements(By.CSS_SELECTOR, "video source")
            for i, source in enumerate(source_elements):
                try:
                    src = source.get_attribute('src')
                    if src:
                        filename = self._generate_video_filename(src, creator_name, post_index, i)
                        videos.append((src, filename))
                except:
                    continue
            
            # Hledej v HTML také
            html = self.driver.page_source
            video_patterns = [
                r'https://[^"\s]+\.mp4[^"\s]*',
                r'https://[^"\s]+\.webm[^"\s]*',
                r'"videoUrl":\s*"([^"]+)"',
            ]
            
            for pattern in video_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    filename = self._generate_video_filename(match, creator_name, post_index, len(videos))
                    videos.append((match, filename))
            
        except Exception as e:
            logger.error(f"Chyba při hledání videí: {e}")
        
        return videos
    
    def _find_documents_selenium(self, creator_name: str, post_index: int) -> List[Tuple[str, str]]:
        """Najdi dokumenty pomocí Selenium"""
        documents = []
        
        try:
            # Najdi odkazy na dokumenty
            doc_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='.pdf'], a[href*='.doc'], a[href*='.zip']")
            
            for i, link in enumerate(doc_links):
                try:
                    href = link.get_attribute('href')
                    if href:
                        filename = self._generate_document_filename(href, creator_name, post_index, i)
                        documents.append((href, filename))
                except:
                    continue
            
        except Exception as e:
            logger.error(f"Chyba při hledání dokumentů: {e}")
        
        return documents
    
    def _is_content_image(self, url: str) -> bool:
        """Zkontroluje, zda je obrázek relevantní obsah"""
        url_lower = url.lower()
        
        # Přeskoč malé obrázky a ikony
        skip_patterns = ['width=32', 'width=64', 'width=96', 'avatar', 'icon', 'thumb', 'logo', 'favicon']
        
        for pattern in skip_patterns:
            if pattern in url_lower:
                return False
        
        return any(ext in url_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp'])
    
    def _generate_image_filename(self, url: str, creator_name: str, post_index: int, img_index: int) -> str:
        """Generuje název souboru pro obrázek"""
        url_parts = url.split('/')
        original_name = url_parts[-1].split('?')[0]
        
        if not original_name or '.' not in original_name:
            original_name = f"image_{img_index:03d}.jpg"
        
        return f"images/{creator_name}_post{post_index:03d}_{original_name}"
    
    def _generate_video_filename(self, url: str, creator_name: str, post_index: int, video_index: int) -> str:
        """Generuje název souboru pro video"""
        if 'youtube.com' in url or 'youtu.be' in url:
            return f"videos/{creator_name}_post{post_index:03d}_youtube_{video_index:03d}.url"
        elif 'vimeo.com' in url:
            return f"videos/{creator_name}_post{post_index:03d}_vimeo_{video_index:03d}.url"
        else:
            url_parts = url.split('/')
            original_name = url_parts[-1].split('?')[0]
            
            if not original_name or '.' not in original_name:
                original_name = f"video_{video_index:03d}.mp4"
            
            return f"videos/{creator_name}_post{post_index:03d}_{original_name}"
    
    def _generate_document_filename(self, url: str, creator_name: str, post_index: int, doc_index: int) -> str:
        """Generuje název souboru pro dokument"""
        url_parts = url.split('/')
        original_name = url_parts[-1].split('?')[0]
        
        if not original_name or '.' not in original_name:
            original_name = f"document_{doc_index:03d}.pdf"
        
        return f"documents/{creator_name}_post{post_index:03d}_{original_name}"


# Registrace parseru
HEROHERO_PARSERS = {
    'herohero': HeroheroSeleniumParser,
}
