#!/usr/bin/env python3
"""
🔓 DRM Bypass pro HeroHero
Kombinuje různé techniky pro obcházení Widevine DRM ochrany
"""

import os
import re
import json
import time
import base64
import logging
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse

logger = logging.getLogger(__name__)

class HeroHeroDRMBypass:
    """Pokročilé obcházení DRM ochrany HeroHero"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """Nastav session s cookies a headers"""
        try:
            from auth_helper import AuthHelper
            cookies = AuthHelper.get_snap_brave_cookies('herohero.co')
            
            for name, value in cookies.items():
                self.session.cookies.set(name, value, domain='.herohero.co')
            
            # Nastav realistické headers
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'cs,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
        except Exception as e:
            logger.warning(f"Problém s nastavením session: {e}")
    
    def extract_drm_content(self, post_url: str) -> List[Dict]:
        """Extrahuj DRM chráněný obsah z příspěvku"""
        content_list = []
        
        try:
            # Metoda 1: Analýza network requestů
            network_content = self._analyze_network_requests(post_url)
            content_list.extend(network_content)
            
            # Metoda 2: JavaScript execution bypass
            js_content = self._javascript_bypass(post_url)
            content_list.extend(js_content)
            
            # Metoda 3: HLS stream extraction
            hls_content = self._extract_hls_streams(post_url)
            content_list.extend(hls_content)
            
            # Metoda 4: API endpoint discovery
            api_content = self._discover_api_endpoints(post_url)
            content_list.extend(api_content)
            
        except Exception as e:
            logger.error(f"Chyba při extrakci DRM obsahu: {e}")
        
        return content_list
    
    def _analyze_network_requests(self, post_url: str) -> List[Dict]:
        """Analyzuj network requesty pro nalezení video streamů"""
        content = []
        
        try:
            # Simuluj načtení stránky a zachyť všechny requesty
            response = self.session.get(post_url)
            
            if response.status_code == 200:
                # Hledej HLS manifesty (.m3u8)
                m3u8_pattern = r'https://[^"\s]+\.m3u8[^"\s]*'
                m3u8_urls = re.findall(m3u8_pattern, response.text, re.IGNORECASE)
                
                for m3u8_url in m3u8_urls:
                    content.append({
                        'type': 'hls_stream',
                        'url': m3u8_url,
                        'method': 'network_analysis'
                    })
                
                # Hledej MP4 streamy
                mp4_pattern = r'https://[^"\s]+\.mp4[^"\s]*'
                mp4_urls = re.findall(mp4_pattern, response.text, re.IGNORECASE)
                
                for mp4_url in mp4_urls:
                    content.append({
                        'type': 'mp4_stream',
                        'url': mp4_url,
                        'method': 'network_analysis'
                    })
        
        except Exception as e:
            logger.debug(f"Network analysis selhala: {e}")
        
        return content
    
    def _javascript_bypass(self, post_url: str) -> List[Dict]:
        """Pokus o bypass pomocí JavaScript execution"""
        content = []
        
        try:
            # Získej HTML stránky
            response = self.session.get(post_url)
            html = response.text
            
            # Hledej JavaScript kód s video daty
            js_patterns = [
                r'"videoUrl":\s*"([^"]+)"',
                r'"streamUrl":\s*"([^"]+)"',
                r'"manifestUrl":\s*"([^"]+)"',
                r'videoSrc:\s*["\']([^"\']+)["\']',
                r'src:\s*["\']([^"\']+\.m3u8[^"\']*)["\']'
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    # Dekóduj případné base64 nebo URL encoding
                    decoded_url = self._decode_url(match)
                    if decoded_url:
                        content.append({
                            'type': 'js_extracted',
                            'url': decoded_url,
                            'method': 'javascript_bypass'
                        })
        
        except Exception as e:
            logger.debug(f"JavaScript bypass selhal: {e}")
        
        return content
    
    def _extract_hls_streams(self, post_url: str) -> List[Dict]:
        """Extrahuj HLS streamy a pokus se je dekódovat"""
        content = []
        
        try:
            response = self.session.get(post_url)
            
            # Najdi všechny možné HLS manifesty
            hls_patterns = [
                r'https://[^"\s]+\.m3u8[^"\s]*',
                r'"manifest":\s*"([^"]+)"',
                r'"playlist":\s*"([^"]+)"'
            ]
            
            hls_urls = set()
            for pattern in hls_patterns:
                matches = re.findall(pattern, response.text, re.IGNORECASE)
                hls_urls.update(matches)
            
            for hls_url in hls_urls:
                try:
                    # Pokus o stažení HLS manifestu
                    manifest_response = self.session.get(hls_url)
                    if manifest_response.status_code == 200:
                        # Analyzuj manifest pro segmenty
                        segments = self._parse_hls_manifest(manifest_response.text, hls_url)
                        
                        content.append({
                            'type': 'hls_manifest',
                            'url': hls_url,
                            'segments': segments,
                            'method': 'hls_extraction'
                        })
                
                except Exception as e:
                    logger.debug(f"HLS manifest analýza selhala pro {hls_url}: {e}")
        
        except Exception as e:
            logger.debug(f"HLS extraction selhal: {e}")
        
        return content
    
    def _discover_api_endpoints(self, post_url: str) -> List[Dict]:
        """Objevuj skryté API endpointy"""
        content = []
        
        try:
            # Extrahuj post ID z URL
            post_id = self._extract_post_id(post_url)
            creator_name = post_url.split('/')[-3]  # Předpokládáme /{creator}/posts/{id}
            
            if post_id and creator_name:
                # Zkus různé API endpointy
                api_endpoints = [
                    f"https://herohero.co/api/posts/{post_id}",
                    f"https://herohero.co/api/posts/{post_id}/media",
                    f"https://herohero.co/api/posts/{post_id}/assets",
                    f"https://herohero.co/api/{creator_name}/posts/{post_id}",
                    f"https://herohero.co/services/posts/{post_id}",
                    f"https://api.herohero.co/posts/{post_id}",
                ]
                
                for endpoint in api_endpoints:
                    try:
                        api_response = self.session.get(endpoint)
                        if api_response.status_code == 200:
                            api_data = api_response.json()
                            
                            # Hledej video URLs v API odpovědi
                            video_urls = self._extract_urls_from_json(api_data)
                            
                            for video_url in video_urls:
                                content.append({
                                    'type': 'api_discovered',
                                    'url': video_url,
                                    'method': 'api_discovery',
                                    'endpoint': endpoint
                                })
                    
                    except Exception as e:
                        logger.debug(f"API endpoint {endpoint} selhal: {e}")
        
        except Exception as e:
            logger.debug(f"API discovery selhal: {e}")
        
        return content
    
    def _decode_url(self, url: str) -> Optional[str]:
        """Dekóduj URL (base64, URL encoding, atd.)"""
        try:
            # Pokus o base64 dekódování
            if len(url) % 4 == 0 and re.match(r'^[A-Za-z0-9+/]*={0,2}$', url):
                try:
                    decoded = base64.b64decode(url).decode('utf-8')
                    if decoded.startswith('http'):
                        return decoded
                except:
                    pass
            
            # URL už je dekódované
            if url.startswith('http'):
                return url
            
            # Relativní URL
            if url.startswith('/'):
                return f"https://herohero.co{url}"
        
        except Exception as e:
            logger.debug(f"URL dekódování selhalo: {e}")
        
        return None
    
    def _parse_hls_manifest(self, manifest_content: str, base_url: str) -> List[str]:
        """Parsuj HLS manifest a získej segmenty"""
        segments = []
        
        try:
            lines = manifest_content.strip().split('\n')
            base_path = '/'.join(base_url.split('/')[:-1])
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    if line.startswith('http'):
                        segments.append(line)
                    else:
                        segments.append(f"{base_path}/{line}")
        
        except Exception as e:
            logger.debug(f"HLS manifest parsing selhal: {e}")
        
        return segments
    
    def _extract_post_id(self, post_url: str) -> Optional[str]:
        """Extrahuj post ID z URL"""
        try:
            # Očekáváme URL ve formátu: https://herohero.co/{creator}/posts/{id}
            parts = post_url.split('/')
            if 'posts' in parts:
                post_index = parts.index('posts')
                if post_index + 1 < len(parts):
                    return parts[post_index + 1]
        except:
            pass
        
        return None
    
    def _extract_urls_from_json(self, data: dict) -> List[str]:
        """Rekurzivně extrahuj URLs z JSON dat"""
        urls = []
        
        def extract_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, str) and (value.startswith('http') and 
                        any(ext in value.lower() for ext in ['.mp4', '.m3u8', '.webm', '.mov'])):
                        urls.append(value)
                    else:
                        extract_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    extract_recursive(item)
        
        extract_recursive(data)
        return urls
    
    def download_drm_content(self, content_info: Dict, output_dir: Path) -> bool:
        """Stáhni DRM chráněný obsah"""
        try:
            if content_info['type'] == 'hls_manifest':
                return self._download_hls_stream(content_info, output_dir)
            elif content_info['type'] in ['mp4_stream', 'js_extracted', 'api_discovered']:
                return self._download_direct_stream(content_info, output_dir)
            else:
                logger.warning(f"Neznámý typ obsahu: {content_info['type']}")
                return False
        
        except Exception as e:
            logger.error(f"Chyba při stahování DRM obsahu: {e}")
            return False
    
    def _download_hls_stream(self, content_info: Dict, output_dir: Path) -> bool:
        """Stáhni HLS stream pomocí ffmpeg"""
        try:
            output_file = output_dir / f"drm_video_{int(time.time())}.mp4"
            
            cmd = [
                'ffmpeg',
                '-i', content_info['url'],
                '-c', 'copy',
                '-bsf:a', 'aac_adtstoasc',
                '-y',
                str(output_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_file.exists():
                logger.info(f"✅ HLS stream stažen: {output_file}")
                return True
            else:
                logger.error(f"ffmpeg selhal: {result.stderr}")
                return False
        
        except Exception as e:
            logger.error(f"HLS download selhal: {e}")
            return False
    
    def _download_direct_stream(self, content_info: Dict, output_dir: Path) -> bool:
        """Stáhni přímý stream"""
        try:
            output_file = output_dir / f"direct_video_{int(time.time())}.mp4"
            
            response = self.session.get(content_info['url'], stream=True, timeout=60)
            response.raise_for_status()
            
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"✅ Přímý stream stažen: {output_file}")
            return True
        
        except Exception as e:
            logger.error(f"Přímé stahování selhalo: {e}")
            return False

def test_drm_bypass():
    """Test DRM bypass"""
    bypass = HeroHeroDRMBypass()
    
    # Test URL (nahraď skutečným)
    test_url = "https://herohero.co/servactiporadi/posts/123"
    
    print(f"🔓 Testuji DRM bypass pro: {test_url}")
    content = bypass.extract_drm_content(test_url)
    
    print(f"✅ Nalezeno {len(content)} DRM chráněných položek:")
    for item in content:
        print(f"  🎬 {item['type']}: {item['url'][:80]}...")

if __name__ == "__main__":
    test_drm_bypass()
